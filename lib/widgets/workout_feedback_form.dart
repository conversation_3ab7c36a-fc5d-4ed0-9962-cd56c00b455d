import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/design_system.dart';

class WorkoutFeedbackForm extends StatefulWidget {
  final int? initialRating;
  final String? initialNotes;
  final Function(int rating, String? notes) onFeedbackSubmitted;
  final VoidCallback? onSkip;

  const WorkoutFeedbackForm({
    super.key,
    this.initialRating,
    this.initialNotes,
    required this.onFeedbackSubmitted,
    this.onSkip,
  });

  @override
  State<WorkoutFeedbackForm> createState() => _WorkoutFeedbackFormState();
}

class _WorkoutFeedbackFormState extends State<WorkoutFeedbackForm> {
  late int _selectedRating;
  final TextEditingController _notesController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _selectedRating = widget.initialRating ?? 3; // Default to happy face
    _notesController.text = widget.initialNotes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppSpacing.lg),
            _buildRatingSection(context),
            const SizedBox(height: AppSpacing.lg),
            _buildNotesSection(context),
            const SizedBox(height: AppSpacing.xl),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Icon(
            Icons.feedback,
            color: AppColors.primary,
            size: 24,
          ),
        ),
        const SizedBox(width: AppSpacing.lg),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'How was your workout?',
                style: AppTypography.heading2.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Your feedback helps us improve your experience',
                style: AppTypography.body2.copyWith(
                  color: AppColorsTheme.textSecondary(context),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRatingSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Overall Rating',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildRatingButton(1, '😞', 'Too Hard'),
            _buildRatingButton(2, '😐', 'Challenging'),
            _buildRatingButton(3, '😊', 'Just Right'),
            _buildRatingButton(4, '😁', 'Easy'),
            _buildRatingButton(5, '🔥', 'Too Easy'),
          ],
        ),
      ],
    );
  }

  Widget _buildRatingButton(int rating, String emoji, String label) {
    final isSelected = _selectedRating == rating;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedRating = rating;
        });
        HapticFeedback.selectionClick();
      },
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: isSelected ? AppColors.primary : AppColorsTheme.surface(context),
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? AppColors.primary : AppColorsTheme.border(context),
                width: 2,
              ),
            ),
            child: Center(
              child: Text(
                emoji,
                style: const TextStyle(fontSize: 24),
              ),
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            label,
            style: AppTypography.caption.copyWith(
              color: isSelected ? AppColors.primary : AppColorsTheme.textSecondary(context),
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Additional Notes (Optional)',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        TextField(
          controller: _notesController,
          maxLines: 4,
          maxLength: 500,
          decoration: InputDecoration(
            hintText:
                'How did the workout feel? Any exercises that were particularly challenging or easy? Any suggestions for improvement?',
            hintStyle: AppTypography.body2.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              borderSide: BorderSide(color: AppColorsTheme.border(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              borderSide: BorderSide(color: AppColorsTheme.border(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.all(AppSpacing.lg),
            counterStyle: AppTypography.caption.copyWith(
              color: AppColorsTheme.textSecondary(context),
            ),
          ),
          style: AppTypography.body1.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isSubmitting ? null : _submitFeedback,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(vertical: AppSpacing.lg),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderRadius.sm),
              ),
              elevation: 0,
            ),
            child: _isSubmitting
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Text(
                        'Submitting...',
                        style: AppTypography.buttonLarge.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.check, size: 20),
                      const SizedBox(width: AppSpacing.sm),
                      Text(
                        'Submit Feedback',
                        style: AppTypography.buttonLarge.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        if (widget.onSkip != null) ...[
          const SizedBox(height: AppSpacing.lg),
          TextButton(
            onPressed: _isSubmitting ? null : widget.onSkip,
            child: Text(
              'Skip for now',
              style: AppTypography.buttonMedium.copyWith(
                color: AppColorsTheme.textSecondary(context),
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _submitFeedback() async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Add a small delay to show loading state
      await Future.delayed(const Duration(milliseconds: 500));

      final notes = _notesController.text.trim();
      widget.onFeedbackSubmitted(
        _selectedRating,
        notes.isEmpty ? null : notes,
      );

      HapticFeedback.mediumImpact();
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit feedback. Please try again.'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
