import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/design_system.dart';

class RestTimerModal extends StatefulWidget {
  final String currentExerciseName;
  final int currentSet;
  final int totalSets;
  final String? nextExerciseName;
  final int restDurationSeconds;
  final VoidCallback onComplete;
  final VoidCallback onSkip;

  const RestTimerModal({
    super.key,
    required this.currentExerciseName,
    required this.currentSet,
    required this.totalSets,
    this.nextExerciseName,
    required this.restDurationSeconds,
    required this.onComplete,
    required this.onSkip,
  });

  @override
  State<RestTimerModal> createState() => _RestTimerModalState();
}

class _RestTimerModalState extends State<RestTimerModal>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  Timer? _timer;
  int _remainingSeconds = 0;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.restDurationSeconds;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _startTimer();
    _animationController.forward();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isPaused && _remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      } else if (_remainingSeconds <= 0) {
        _completeRest();
      }
    });
  }

  void _togglePause() {
    setState(() {
      _isPaused = !_isPaused;
    });
    HapticFeedback.selectionClick();
  }

  void _completeRest() async {
    _timer?.cancel();
    HapticFeedback.mediumImpact();
    await _animationController.reverse();
    widget.onComplete();
  }

  void _skipRest() async {
    _timer?.cancel();
    HapticFeedback.lightImpact();
    await _animationController.reverse();
    widget.onSkip();
  }

  void _adjustTime(int seconds) {
    setState(() {
      _remainingSeconds = (_remainingSeconds + seconds).clamp(0, 300);
    });
    HapticFeedback.selectionClick();
  }

  Widget _buildTimeButton(String label, int seconds) {
    return GestureDetector(
      onTap: () => _adjustTime(seconds),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: AppColorsTheme.surfaceVariant(context),
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColorsTheme.border(context),
          ),
        ),
        child: Center(
          child: Text(
            label,
            style: AppTypography.buttonMedium.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;
    final progress = 1.0 - (_remainingSeconds / widget.restDurationSeconds);

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: WorkoutScaffold(
          showBottomNavigation: false,
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                // Exercise info
                Text(
                  'Rest Time',
                  style: AppTypography.heading2.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                Text(
                  widget.currentExerciseName,
                  style: AppTypography.body1.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Set ${widget.currentSet} / ${widget.totalSets}',
                  style: AppTypography.caption.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
                
                const SizedBox(height: AppSpacing.xxl),
                
                // Timer display
                Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColorsTheme.border(context),
                      width: 8,
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Progress indicator
                      Positioned.fill(
                        child: CircularProgressIndicator(
                          value: progress,
                          strokeWidth: 8,
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                        ),
                      ),
                      // Timer text
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}',
                              style: AppTypography.display1.copyWith(
                                color: AppColorsTheme.textPrimary(context),
                                fontSize: 36,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _isPaused ? 'Paused' : 'remaining',
                              style: AppTypography.caption.copyWith(
                                color: AppColorsTheme.textSecondary(context),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: AppSpacing.xxl),
                
                // Time adjustment buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildTimeButton('-15s', -15),
                    const SizedBox(width: AppSpacing.lg),
                    _buildTimeButton('+15s', 15),
                  ],
                ),
                
                const SizedBox(height: AppSpacing.xl),
                
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: AppButton(
                        onPressed: _togglePause,
                        text: _isPaused ? 'Resume' : 'Pause',
                        variant: AppButtonVariant.ghost,
                        icon: _isPaused ? Icons.play_arrow : Icons.pause,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Expanded(
                      child: AppButton(
                        onPressed: _skipRest,
                        text: 'Skip Rest',
                        icon: Icons.skip_next,
                      ),
                    ),
                  ],
                ),

                // Next exercise preview
                if (widget.nextExerciseName != null) ...[
                  const SizedBox(height: AppSpacing.xl),
                  AppCard(
                    child: Row(
                      children: [
                        Icon(
                          Icons.fitness_center,
                          color: AppColorsTheme.textSecondary(context),
                          size: 20,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Next:',
                                style: AppTypography.caption.copyWith(
                                  color: AppColorsTheme.textSecondary(context),
                                ),
                              ),
                              Text(
                                widget.nextExerciseName!,
                                style: AppTypography.body2.copyWith(
                                  color: AppColorsTheme.textPrimary(context),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          color: AppColors.primary,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
