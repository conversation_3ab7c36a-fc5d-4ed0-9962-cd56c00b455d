import 'package:flutter/material.dart';
import '../../../models/onboarding_data.dart';
import '../../../design_system/design_system.dart';

class FitnessGoalsStep extends StatefulWidget {
  final OnboardingData onboardingData;
  final VoidCallback onDataChanged;

  const FitnessGoalsStep({
    super.key,
    required this.onboardingData,
    required this.onDataChanged,
  });

  @override
  State<FitnessGoalsStep> createState() => _FitnessGoalsStepState();
}

class _FitnessGoalsStepState extends State<FitnessGoalsStep>
    with TickerProviderStateMixin {
  final List<String> _selectedGoals = [];
  String? _primaryGoal;
  final TextEditingController _notesController = TextEditingController();
  final Map<String, AnimationController> _cardAnimations = {};

  @override
  void initState() {
    super.initState();
    _updateFromData();

    // Initialize card animations
    for (final goal in OnboardingConstants.fitnessGoals) {
      _cardAnimations[goal] = AnimationController(
        duration: const Duration(milliseconds: 150),
        vsync: this,
      );
    }
  }

  @override
  void didUpdateWidget(FitnessGoalsStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateFromData();
  }

  void _updateFromData() {
    _selectedGoals.clear();
    _selectedGoals.addAll(widget.onboardingData.fitnessGoals);
    _primaryGoal = widget.onboardingData.primaryGoal;
    _notesController.text = widget.onboardingData.additionalNotes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    for (final controller in _cardAnimations.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _updateData() {
    if (_primaryGoal == null && _selectedGoals.isNotEmpty) {
      _primaryGoal = _selectedGoals.first;
    }

    widget.onboardingData.fitnessGoals = List.from(_selectedGoals);
    widget.onboardingData.primaryGoal = _primaryGoal;
    widget.onboardingData.additionalNotes = _notesController.text;
    widget.onDataChanged();
  }

  IconData _getGoalIcon(String goal) {
    switch (goal) {
      case 'Training for a specific sport':
        return Icons.sports;
      case 'Increase strength':
        return Icons.fitness_center;
      case 'Increase stamina':
        return Icons.directions_run;
      case 'Optimize Health and Fitness':
        return Icons.favorite;
      case 'Build muscle mass and size':
        return Icons.sports_gymnastics;
      case 'Weight loss':
        return Icons.trending_down;
      case 'Improve Flexibility':
        return Icons.self_improvement;
      case 'General Fitness':
        return Icons.health_and_safety;
      case 'Stress Relief':
        return Icons.spa;
      default:
        return Icons.star;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select all that apply',
          style: AppTypography.body1.copyWith(
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
        const SizedBox(height: 24),

        // Available goals to select from
        ...OnboardingConstants.fitnessGoals.map((goal) {
          final isSelected = _selectedGoals.contains(goal);

          return AnimatedBuilder(
            animation: _cardAnimations[goal]!,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 - (_cardAnimations[goal]!.value * 0.05),
                child: GestureDetector(
                  onTapDown: (_) => _cardAnimations[goal]!.forward(),
                  onTapUp: (_) => _cardAnimations[goal]!.reverse(),
                  onTapCancel: () => _cardAnimations[goal]!.reverse(),
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        _selectedGoals.remove(goal);
                        if (_primaryGoal == goal) {
                          _primaryGoal =
                              _selectedGoals.isNotEmpty ? _selectedGoals.first : null;
                        }
                      } else {
                        _selectedGoals.add(goal);
                        if (_selectedGoals.length == 1) {
                          _primaryGoal = goal;
                        }
                      }
                      _updateData();
                    });
                  },
            child: AppCard(
              margin: const EdgeInsets.only(bottom: AppSpacing.md),
              padding: const EdgeInsets.all(AppSpacing.lg),
              borderRadius: AppBorderRadius.cardRadius,
              backgroundColor: isSelected
                  ? AppColors.primary.withValues(alpha: 0.08)
                  : AppColorsTheme.surface(context),
              border: Border.all(
                color: isSelected
                    ? AppColors.primary
                    : AppColorsTheme.border(context),
                width: isSelected ? 2 : 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.15),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : AppShadows.elevation1,
              child: Row(
                children: [
                  Icon(
                    _getGoalIcon(goal),
                    size: 24,
                    color: isSelected
                        ? AppColors.primary
                        : AppColorsTheme.textSecondary(context),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          goal,
                          style: AppTypography.body1.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isSelected
                                ? AppColors.primary
                                : AppColorsTheme.textPrimary(context),
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          _getGoalDescription(goal),
                          style: AppTypography.body2.copyWith(
                            color: AppColorsTheme.textSecondary(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
                ),
              );
            },
          );
        }),

        if (_selectedGoals.isNotEmpty) ...[
          const SizedBox(height: 32),
          Text(
            'Sort them in your preferred order',
            style: AppTypography.heading3.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 16),

          // Selected goals with reorder handles
          ..._selectedGoals.map((goal) {
            return AppCard(
              margin: const EdgeInsets.only(bottom: AppSpacing.sm),
              padding: const EdgeInsets.all(AppSpacing.lg),
              backgroundColor: AppColorsTheme.surface(context),
              border: Border.all(
                color: AppColors.primary,
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
              child: Row(
                children: [
                  Icon(
                    _getGoalIcon(goal),
                    size: 20,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Text(
                      goal,
                      style: AppTypography.body1.copyWith(
                        fontWeight: FontWeight.w500,
                        color: AppColorsTheme.textPrimary(context),
                      ),
                    ),
                  ),
                  Icon(
                    Icons.drag_handle,
                    color: AppColorsTheme.textSecondary(context),
                    size: 20,
                  ),
                ],
              ),
            );
          }),

          const SizedBox(height: 32),

          // Health coach question
          Text(
            'Is there anything else I should know as your personal health coach?',
            style: AppTypography.heading3.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: _notesController,
            maxLines: 4,
            onChanged: (value) => _updateData(),
            decoration: InputDecoration(
              hintText:
                  'Share any specific needs, preferences, or considerations...',
              hintStyle: AppTypography.body2.copyWith(
                color: AppColorsTheme.textSecondary(context),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppBorderRadius.md),
                borderSide: BorderSide(color: AppColorsTheme.border(context)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppBorderRadius.md),
                borderSide: BorderSide(color: AppColorsTheme.border(context)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppBorderRadius.md),
                borderSide: BorderSide(color: AppColors.primary, width: 2),
              ),
              filled: true,
              fillColor: AppColorsTheme.surface(context),
              contentPadding: const EdgeInsets.all(AppSpacing.lg),
            ),
            style: AppTypography.body1.copyWith(
              color: AppColorsTheme.textPrimary(context),
            ),
          ),
        ],
      ],
    );
  }

  String _getGoalDescription(String goal) {
    switch (goal) {
      case 'Training for a specific sport':
        return 'Example: Marathon, Volleyball, Tactical Readiness';
      case 'Increase strength':
        return 'Low-rep, high-weight training with longer rest periods for maximum power';
      case 'Increase stamina':
        return 'Focus on building stamina and cardiovascular fitness through endurance exercises';
      case 'Optimize Health and Fitness':
        return 'Overall health and wellness improvement';
      case 'Build muscle mass and size':
        return 'Increase muscle mass and strength through resistance training';
      case 'Weight loss':
        return 'Burn calories and reduce body fat';
      case 'Improve Flexibility':
        return 'Enhance mobility and range of motion';
      case 'General Fitness':
        return 'Overall health and wellness';
      case 'Stress Relief':
        return 'Mental health and relaxation';
      default:
        return 'Improve your fitness journey';
    }
  }
}
