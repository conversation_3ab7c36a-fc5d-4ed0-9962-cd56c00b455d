import 'package:flutter/material.dart';
import '../../../models/onboarding_data.dart';
import '../../../design_system/design_system.dart';

class WorkoutPreferencesStep extends StatefulWidget {
  final OnboardingData onboardingData;
  final VoidCallback onDataChanged;

  const WorkoutPreferencesStep({
    super.key,
    required this.onboardingData,
    required this.onDataChanged,
  });

  @override
  State<WorkoutPreferencesStep> createState() => _WorkoutPreferencesStepState();
}

class _WorkoutPreferencesStepState extends State<WorkoutPreferencesStep> {
  int _workoutFrequency = 2;
  int _workoutDuration = 45; // in minutes
  final List<String> _selectedDays = [];
  final List<String> _selectedEnvironments = [];

  @override
  void initState() {
    super.initState();
    _updateFromData();
  }

  @override
  void didUpdateWidget(WorkoutPreferencesStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateFromData();
  }

  void _updateFromData() {
    _workoutFrequency = widget.onboardingData.workoutFrequency ?? 2;
    // Convert duration string to minutes if it exists
    if (widget.onboardingData.workoutDuration != null) {
      _workoutDuration =
          _parseDurationToMinutes(widget.onboardingData.workoutDuration!);
    }
    _selectedDays.clear();
    _selectedDays.addAll(widget.onboardingData.workoutDays);
    _selectedEnvironments.clear();
    _selectedEnvironments.addAll(widget.onboardingData.workoutEnvironment);
  }

  int _parseDurationToMinutes(String duration) {
    if (duration.contains('15-30')) return 30;
    if (duration.contains('30-45')) return 45;
    if (duration.contains('45-60')) return 60;
    if (duration.contains('60+')) return 75;
    return 45; // default
  }

  String _formatDurationFromMinutes(int minutes) {
    if (minutes <= 30) return '15-30 minutes';
    if (minutes <= 45) return '30-45 minutes';
    if (minutes <= 60) return '45-60 minutes';
    return '60+ minutes';
  }

  void _generateWorkoutDays() {
    _selectedDays.clear();
    final days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];

    if (_workoutFrequency == 1) {
      _selectedDays.add('Monday');
    } else if (_workoutFrequency == 2) {
      _selectedDays.addAll(['Monday', 'Thursday']);
    } else if (_workoutFrequency == 3) {
      _selectedDays.addAll(['Monday', 'Wednesday', 'Friday']);
    } else if (_workoutFrequency == 4) {
      _selectedDays.addAll(['Monday', 'Tuesday', 'Thursday', 'Friday']);
    } else if (_workoutFrequency == 5) {
      _selectedDays
          .addAll(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']);
    } else if (_workoutFrequency == 6) {
      _selectedDays.addAll(
          ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']);
    } else {
      _selectedDays.addAll(days);
    }
  }

  bool _isWorkoutDay(String shortDay) {
    final dayMap = {
      'Mon': 'Monday',
      'Tue': 'Tuesday',
      'Wed': 'Wednesday',
      'Thu': 'Thursday',
      'Fri': 'Friday',
      'Sat': 'Saturday',
      'Sun': 'Sunday',
    };
    return _selectedDays.contains(dayMap[shortDay]);
  }

  void _updateData() {
    _generateWorkoutDays();

    widget.onboardingData.workoutFrequency = _workoutFrequency;
    widget.onboardingData.workoutDuration =
        _formatDurationFromMinutes(_workoutDuration);
    widget.onboardingData.workoutDays = _selectedDays;
    widget.onboardingData.workoutEnvironment =
        _selectedEnvironments.isEmpty ? ['Home'] : _selectedEnvironments;
    widget.onDataChanged();
  }

  @override
  Widget build(BuildContext context) {
    _generateWorkoutDays(); // Update workout days when frequency changes

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Removed redundant subtitle as it's already in the header

        // Workout frequency
        Text(
          'What should your number of workouts per week goal to be?',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Science says between at least 2 and optimally 4',
          style: AppTypography.body2.copyWith(
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Frequency dropdown - Improved design
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg, vertical: AppSpacing.lg),
          decoration: BoxDecoration(
            border: Border.all(color: AppColorsTheme.borderLight(context)),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            color: AppColorsTheme.cardBackground(context),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: _workoutFrequency,
              isExpanded: true,
              dropdownColor: AppColorsTheme.cardBackground(context),
              items: List.generate(7, (index) => index + 1).map((frequency) {
                return DropdownMenuItem<int>(
                  value: frequency,
                  child: Text(
                    '$frequency days',
                    style: AppTypography.body1.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _workoutFrequency = value!;
                });
                _updateData();
              },
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: AppColorsTheme.textSecondary(context),
              ),
            ),
          ),
        ),

        const SizedBox(height: AppSpacing.sectionSpacing),

        // Workout duration
        Text(
          'How long do you like your workout to be?',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          '$_workoutDuration minutes',
          style: AppTypography.body1.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Duration slider - Improved design
        Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: AppColorsTheme.cardBackground(context),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            border: Border.all(color: AppColorsTheme.borderLight(context)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppColors.primary,
                  inactiveTrackColor: AppColorsTheme.borderLight(context),
                  thumbColor: AppColors.primary,
                  overlayColor: AppColors.primary.withValues(alpha: 0.1),
                  trackHeight: 6,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
                ),
                child: Slider(
                  value: _workoutDuration.toDouble(),
                  min: 15,
                  max: 90,
                  divisions: 15,
                  onChanged: (value) {
                    setState(() {
                      _workoutDuration = value.round();
                    });
                    _updateData();
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '15 min',
                    style: AppTypography.caption.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  ),
                  Text(
                    '90 min',
                    style: AppTypography.caption.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: AppSpacing.sectionSpacing),

        // Day preference checkbox - Improved design
        Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: AppColorsTheme.cardBackground(context),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            border: Border.all(color: AppColorsTheme.borderLight(context)),
          ),
          child: Row(
            children: [
              Checkbox(
                value: false, // This would need to be implemented based on user preference
                onChanged: (value) {
                  // Handle no preference selection
                },
                activeColor: AppColors.primary,
                checkColor: Colors.white,
                side: BorderSide(color: AppColorsTheme.borderLight(context)),
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Text(
                  'No preference, optimize the time for me',
                  style: AppTypography.body2.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppSpacing.xl),

        // Day preference section
        Text(
          'Day preference',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'This can change based on your calendar',
          style: AppTypography.body2.copyWith(
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Calendar grid - Improved design and contrast
        Container(
          padding: const EdgeInsets.all(AppSpacing.xl),
          decoration: BoxDecoration(
            color: AppColorsTheme.cardBackground(context),
            border: Border.all(color: AppColorsTheme.borderLight(context)),
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Days header - Improved contrast
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: ['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((dayLetter) {
                  return Text(
                    dayLetter,
                    style: AppTypography.caption.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColorsTheme.textSecondary(context),
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: AppSpacing.lg),

              // Day circles - Enhanced design
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
                    .map((day) {
                  final isWorkoutDay = _isWorkoutDay(day);
                  return Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isWorkoutDay
                          ? AppColors.primary
                          : Colors.transparent,
                      border: Border.all(
                        color: isWorkoutDay
                            ? AppColors.primary
                            : AppColorsTheme.borderLight(context),
                        width: 2,
                      ),
                    ),
                    child: isWorkoutDay
                        ? const Icon(
                            Icons.check,
                            size: 18,
                            color: Colors.white,
                          )
                        : null,
                  );
                }).toList(),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Legend - Improved design
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primary,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      Text(
                        'Workout days',
                        style: AppTypography.caption.copyWith(
                          color: AppColorsTheme.textSecondary(context),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: AppSpacing.lg),
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppColorsTheme.borderLight(context),
                            width: 2,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      Text(
                        'Rest days',
                        style: AppTypography.caption.copyWith(
                          color: AppColorsTheme.textSecondary(context),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
