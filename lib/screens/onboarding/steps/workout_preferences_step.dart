import 'package:flutter/material.dart';
import '../../../models/onboarding_data.dart';
import '../../../design_system/design_system.dart';

class WorkoutPreferencesStep extends StatefulWidget {
  final OnboardingData onboardingData;
  final VoidCallback onDataChanged;

  const WorkoutPreferencesStep({
    super.key,
    required this.onboardingData,
    required this.onDataChanged,
  });

  @override
  State<WorkoutPreferencesStep> createState() => _WorkoutPreferencesStepState();
}

class _WorkoutPreferencesStepState extends State<WorkoutPreferencesStep> {
  int _workoutFrequency = 2;
  int _workoutDuration = 45; // in minutes
  final List<String> _selectedDays = [];
  final List<String> _selectedEnvironments = [];

  @override
  void initState() {
    super.initState();
    _updateFromData();
  }

  @override
  void didUpdateWidget(WorkoutPreferencesStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateFromData();
  }

  void _updateFromData() {
    _workoutFrequency = widget.onboardingData.workoutFrequency ?? 2;
    // Convert duration string to minutes if it exists
    if (widget.onboardingData.workoutDuration != null) {
      _workoutDuration =
          _parseDurationToMinutes(widget.onboardingData.workoutDuration!);
    }
    _selectedDays.clear();
    _selectedDays.addAll(widget.onboardingData.workoutDays);
    _selectedEnvironments.clear();
    _selectedEnvironments.addAll(widget.onboardingData.workoutEnvironment);
  }

  int _parseDurationToMinutes(String duration) {
    if (duration.contains('15-30')) return 30;
    if (duration.contains('30-45')) return 45;
    if (duration.contains('45-60')) return 60;
    if (duration.contains('60+')) return 75;
    return 45; // default
  }

  String _formatDurationFromMinutes(int minutes) {
    if (minutes <= 30) return '15-30 minutes';
    if (minutes <= 45) return '30-45 minutes';
    if (minutes <= 60) return '45-60 minutes';
    return '60+ minutes';
  }

  void _generateWorkoutDays() {
    _selectedDays.clear();
    final days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];

    if (_workoutFrequency == 1) {
      _selectedDays.add('Monday');
    } else if (_workoutFrequency == 2) {
      _selectedDays.addAll(['Monday', 'Thursday']);
    } else if (_workoutFrequency == 3) {
      _selectedDays.addAll(['Monday', 'Wednesday', 'Friday']);
    } else if (_workoutFrequency == 4) {
      _selectedDays.addAll(['Monday', 'Tuesday', 'Thursday', 'Friday']);
    } else if (_workoutFrequency == 5) {
      _selectedDays
          .addAll(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']);
    } else if (_workoutFrequency == 6) {
      _selectedDays.addAll(
          ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']);
    } else {
      _selectedDays.addAll(days);
    }
  }

  bool _isWorkoutDay(String shortDay) {
    final dayMap = {
      'Mon': 'Monday',
      'Tue': 'Tuesday',
      'Wed': 'Wednesday',
      'Thu': 'Thursday',
      'Fri': 'Friday',
      'Sat': 'Saturday',
      'Sun': 'Sunday',
    };
    return _selectedDays.contains(dayMap[shortDay]);
  }

  void _updateData() {
    _generateWorkoutDays();

    widget.onboardingData.workoutFrequency = _workoutFrequency;
    widget.onboardingData.workoutDuration =
        _formatDurationFromMinutes(_workoutDuration);
    widget.onboardingData.workoutDays = _selectedDays;
    widget.onboardingData.workoutEnvironment =
        _selectedEnvironments.isEmpty ? ['Home'] : _selectedEnvironments;
    widget.onDataChanged();
  }

  @override
  Widget build(BuildContext context) {
    _generateWorkoutDays(); // Update workout days when frequency changes

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'When science meets your lifestyle',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 32),

        // Workout frequency
        const Text(
          'What should your number of workouts per week goal to be?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Science says between at least 2 and optimally 4',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),

        // Frequency dropdown
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: _workoutFrequency,
              isExpanded: true,
              items: List.generate(7, (index) => index + 1).map((frequency) {
                return DropdownMenuItem<int>(
                  value: frequency,
                  child: Text(
                    '$frequency days',
                    style: const TextStyle(fontSize: 16),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _workoutFrequency = value!;
                });
                _updateData();
              },
            ),
          ),
        ),

        const SizedBox(height: 32),

        // Workout duration
        const Text(
          'How long do you like your workout to be?',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          '45 minutes',
          style: TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),

        // Duration slider
        Column(
          children: [
            Slider(
              value: _workoutDuration.toDouble(),
              min: 15,
              max: 90,
              divisions: 15,
              activeColor: const Color(0xFF6366F1),
              onChanged: (value) {
                setState(() {
                  _workoutDuration = value.round();
                });
                _updateData();
              },
            ),
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('15 min',
                    style: TextStyle(fontSize: 12, color: Colors.grey)),
                Text('90 min',
                    style: TextStyle(fontSize: 12, color: Colors.grey)),
              ],
            ),
          ],
        ),

        const SizedBox(height: 32),

        // Day preference checkbox
        Row(
          children: [
            Checkbox(
              value:
                  false, // This would need to be implemented based on user preference
              onChanged: (value) {
                // Handle no preference selection
              },
              activeColor: const Color(0xFF6366F1),
            ),
            const Text(
              'no preference, optimize the time for me',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Day preference section
        const Text(
          'Day preference',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'This can change based on your calendar',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 16),

        // Calendar grid
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[200]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              // Days header
              const Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text('S',
                      style: TextStyle(
                          fontWeight: FontWeight.w500, color: Colors.grey)),
                  Text('M',
                      style: TextStyle(
                          fontWeight: FontWeight.w500, color: Colors.grey)),
                  Text('T',
                      style: TextStyle(
                          fontWeight: FontWeight.w500, color: Colors.grey)),
                  Text('W',
                      style: TextStyle(
                          fontWeight: FontWeight.w500, color: Colors.grey)),
                  Text('T',
                      style: TextStyle(
                          fontWeight: FontWeight.w500, color: Colors.grey)),
                  Text('F',
                      style: TextStyle(
                          fontWeight: FontWeight.w500, color: Colors.grey)),
                  Text('S',
                      style: TextStyle(
                          fontWeight: FontWeight.w500, color: Colors.grey)),
                ],
              ),
              const SizedBox(height: 12),

              // Day circles
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
                    .map((day) {
                  final isWorkoutDay = _isWorkoutDay(day);
                  return Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isWorkoutDay
                          ? const Color(0xFF6366F1)
                          : Colors.transparent,
                      border: Border.all(
                        color: isWorkoutDay
                            ? const Color(0xFF6366F1)
                            : Colors.grey[300]!,
                      ),
                    ),
                    child: isWorkoutDay
                        ? const Icon(Icons.circle,
                            size: 16, color: Colors.white)
                        : null,
                  );
                }).toList(),
              ),

              const SizedBox(height: 16),

              // Legend
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Icon(Icons.circle, size: 12, color: Color(0xFF6366F1)),
                      SizedBox(width: 4),
                      Text('Workout days',
                          style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                  SizedBox(width: 16),
                  Row(
                    children: [
                      Icon(Icons.circle_outlined, size: 12, color: Colors.grey),
                      SizedBox(width: 4),
                      Text('Rest days',
                          style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
