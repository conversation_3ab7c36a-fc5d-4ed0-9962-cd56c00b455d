import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/workout.dart';
import '../services/workout_service.dart';
import '../design_system/design_system.dart';
import '../widgets/set_feedback_modal.dart';
import '../widgets/rest_timer_modal.dart';
import '../widgets/offline_status_indicator.dart';
import 'workout_completion_screen.dart';

class WorkoutSessionScreen extends StatefulWidget {
  final Workout workout;

  const WorkoutSessionScreen({
    super.key,
    required this.workout,
  });

  @override
  State<WorkoutSessionScreen> createState() => _WorkoutSessionScreenState();
}

class _WorkoutSessionScreenState extends State<WorkoutSessionScreen> {
  final WorkoutService _workoutService = WorkoutService();

  // Timer variables
  late Timer _timer;
  int _elapsedSeconds = 0;

  // Workout progress
  int _currentExerciseIndex = 0;
  int _currentSet = 1;
  List<List<Map<String, dynamic>>> _completedSets = [];

  // Input controllers
  final TextEditingController _repsController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();

  // Feedback modal state
  bool _showingFeedbackModal = false;
  int? _pendingReps;
  int? _pendingWeight;

  // Rest timer state
  bool _showingRestTimer = false;

  @override
  void initState() {
    super.initState();
    _initializeWorkout();
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    _repsController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  void _initializeWorkout() {
    // Initialize completed sets tracking
    _completedSets = List.generate(
      widget.workout.exercises.length,
      (index) => <Map<String, dynamic>>[],
    );

    // Set initial values from workout plan
    final currentExercise = widget.workout.exercises[_currentExerciseIndex];
    if (currentExercise.reps.isNotEmpty) {
      _repsController.text = currentExercise.reps[_currentSet - 1].toString();
    }
    if (currentExercise.weight != null && currentExercise.weight!.isNotEmpty) {
      _weightController.text =
          currentExercise.weight![_currentSet - 1].toString();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _elapsedSeconds++;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return WorkoutScaffold(
      onClose: _showExitDialog,
      showBottomNavigation: !(_showingFeedbackModal || _showingRestTimer),
      currentNavigationItem: AppNavigationItem.workouts,
      onNavigationTap: (item) => _handleNavigation(context, item),
      scrollable: true,
      bodyPadding: const EdgeInsets.only(
        left: 24,
        right: 24,
        top: 16,
        bottom: 16,
      ),
      body: Column(
        children: [
          // Offline Status Indicator
          const OfflineStatusIndicator(compact: true),
          const SizedBox(height: 16),
          _buildTimerAndProgress(),
          const SizedBox(height: 40),
          _buildCurrentExercise(),
          const SizedBox(height: 40),
          _buildSetInputs(),
          const SizedBox(height: 40),
          _buildNextExercisePreview(),
          SizedBox(height: MediaQuery.of(context).padding.bottom + 100), // Safe area + nav space
        ],
      ),
      overlays: [
          // Feedback Modal Overlay with smooth fade transition
          if (_showingFeedbackModal &&
              _pendingReps != null &&
              _pendingWeight != null)
            Positioned.fill(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 500),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: Tween<double>(
                      begin: 0.0,
                      end: 1.0,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOutCubic,
                    )),
                    child: ScaleTransition(
                      scale: Tween<double>(
                        begin: 0.95,
                        end: 1.0,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOutCubic,
                      )),
                      child: child,
                    ),
                  );
                },
                child: SetFeedbackModal(
                  key: const ValueKey('feedback_modal'),
                  completedReps: _pendingReps!,
                  completedWeight: _pendingWeight!,
                  exerciseName: widget
                      .workout.exercises[_currentExerciseIndex].exercise.name,
                  currentSet: _currentSet,
                  totalSets:
                      widget.workout.exercises[_currentExerciseIndex].sets,
                  onComplete: _onFeedbackComplete,
                  onFeedbackSubmitted: _onFeedbackSubmitted,
                ),
              ),
            ),
          // Rest Timer Modal Overlay with smooth fade transition
          if (_showingRestTimer)
            Positioned.fill(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 600),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: Tween<double>(
                      begin: 0.0,
                      end: 1.0,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOutCubic,
                    )),
                    child: ScaleTransition(
                      scale: Tween<double>(
                        begin: 0.95,
                        end: 1.0,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOutCubic,
                      )),
                      child: child,
                    ),
                  );
                },
                child: RestTimerModal(
                  key: const ValueKey('rest_timer'),
                  currentExerciseName: widget
                      .workout.exercises[_currentExerciseIndex].exercise.name,
                  currentSet:
                      _currentSet - 1, // Previous set that was completed
                  totalSets:
                      widget.workout.exercises[_currentExerciseIndex].sets,
                  nextExerciseName: _getNextExerciseInfo(),
                  restDurationSeconds: _getRestDuration(),
                  onComplete: _onRestComplete,
                  onSkip: _onRestComplete,
                ),
              ),
            ),
      ],
    );
  }

  void _handleNavigation(BuildContext context, AppNavigationItem item) {
    // Don't navigate if we're currently in the workouts section
    if (item == AppNavigationItem.workouts) return;

    // Show confirmation dialog before leaving the active workout
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: AppBorderRadius.cardRadius,
        ),
        title: const Text('Leave Workout?'),
        content: const Text(
          'Your progress will be saved, but the workout will remain active. You can continue later.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              navigateToItem(context, item);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primary,
            ),
            child: const Text('Leave'),
          ),
        ],
      ),
    );
  }



  Widget _buildTimerAndProgress() {
    final progress = _calculateProgress();
    final elapsedTime = _formatElapsedTime(_elapsedSeconds);

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  elapsedTime,
                  style: AppTypography.display1.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontSize: 48,
                    fontWeight: FontWeight.w300,
                    letterSpacing: -1,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Elapsed Time',
                  style: AppTypography.body2.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${(progress * 100).round()}%',
                  style: AppTypography.display1.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontSize: 48,
                    fontWeight: FontWeight.w300,
                    letterSpacing: -1,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Complete',
                  style: AppTypography.body2.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildProgressDots(),
      ],
    );
  }

  Widget _buildProgressDots() {
    final totalExercises = widget.workout.exercises.length;
    final currentExercise = _currentExerciseIndex;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(totalExercises, (index) {
        final isCompleted = index < currentExercise;
        final isCurrent = index == currentExercise;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: isCurrent ? 32 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: isCompleted || isCurrent
                ? AppColors.primary
                : AppColorsTheme.border(context),
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }),
    );
  }

  Widget _buildCurrentExercise() {
    final currentExercise = widget.workout.exercises[_currentExerciseIndex];
    final totalSets = currentExercise.sets;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Now:',
          style: AppTypography.body1.copyWith(
            color: AppColorsTheme.textSecondary(context),
            fontSize: 18,
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: Text(
                currentExercise.exercise.name,
                style: AppTypography.heading1.copyWith(
                  color: AppColorsTheme.textPrimary(context),
                  fontSize: 28,
                  fontWeight: FontWeight.w600,
                  height: 1.2,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              'Set $_currentSet / $totalSets',
              style: AppTypography.body1.copyWith(
                color: AppColorsTheme.textSecondary(context),
                fontSize: 18,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSetInputs() {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: _completeCurrentSet,
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                color: AppColorsTheme.surface(context),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColorsTheme.borderLight(context),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextField(
                    controller: _repsController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    textAlign: TextAlign.center,
                    style: AppTypography.display1.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                      fontSize: 48,
                      fontWeight: FontWeight.w600,
                    ),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: '12',
                      hintStyle: AppTypography.display1.copyWith(
                        color: AppColorsTheme.textSecondary(context).withValues(alpha: 0.3),
                        fontSize: 48,
                        fontWeight: FontWeight.w600,
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Reps',
                    style: AppTypography.body2.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: GestureDetector(
            onTap: _completeCurrentSet,
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                color: AppColorsTheme.surface(context),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColorsTheme.borderLight(context),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextField(
                    controller: _weightController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    textAlign: TextAlign.center,
                    style: AppTypography.display1.copyWith(
                      color: AppColorsTheme.textPrimary(context),
                      fontSize: 48,
                      fontWeight: FontWeight.w600,
                    ),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: '25',
                      hintStyle: AppTypography.display1.copyWith(
                        color: AppColorsTheme.textSecondary(context).withValues(alpha: 0.3),
                        fontSize: 48,
                        fontWeight: FontWeight.w600,
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'lbs',
                    style: AppTypography.body2.copyWith(
                      color: AppColorsTheme.textSecondary(context),
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNextExercisePreview() {
    if (_currentExerciseIndex >= widget.workout.exercises.length - 1 &&
        _currentSet >= widget.workout.exercises[_currentExerciseIndex].sets) {
      // This is the last set of the last exercise
      return Container(
        width: double.infinity,
        height: 80,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppColors.success, AppColors.success.withValues(alpha: 0.8)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _completeWorkout,
            borderRadius: BorderRadius.circular(16),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Complete Workout',
                    style: AppTypography.heading3.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    // Show next exercise or next set
    String nextText;
    String nextExerciseName;
    String? nextExerciseVideoUrl;

    if (_currentSet < widget.workout.exercises[_currentExerciseIndex].sets) {
      // Next set of current exercise
      nextText = 'Next:';
      nextExerciseName =
          widget.workout.exercises[_currentExerciseIndex].exercise.name;
      nextExerciseVideoUrl = widget.workout.exercises[_currentExerciseIndex].exercise.videoUrl;
    } else if (_currentExerciseIndex < widget.workout.exercises.length - 1) {
      // Next exercise
      nextText = 'Next:';
      nextExerciseName =
          widget.workout.exercises[_currentExerciseIndex + 1].exercise.name;
      nextExerciseVideoUrl = widget.workout.exercises[_currentExerciseIndex + 1].exercise.videoUrl;
    } else {
      // This shouldn't happen since we handle workout completion above
      nextText = 'Next:';
      nextExerciseName = 'Workout Complete';
      nextExerciseVideoUrl = null;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColorsTheme.surface(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColorsTheme.borderLight(context),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: nextExerciseVideoUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      _getExerciseThumbnail(nextExerciseVideoUrl),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          const Icon(
                        Icons.fitness_center,
                        color: AppColors.primary,
                        size: 28,
                      ),
                    ),
                  )
                : const Icon(
                    Icons.fitness_center,
                    color: AppColors.primary,
                    size: 28,
                  ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  nextText,
                  style: AppTypography.body2.copyWith(
                    color: AppColorsTheme.textSecondary(context),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  nextExerciseName,
                  style: AppTypography.heading3.copyWith(
                    color: AppColorsTheme.textPrimary(context),
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: _completeCurrentSet,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.fast_forward,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getExerciseThumbnail(String videoUrl) {
    // For Vimeo URLs, generate thumbnail
    if (videoUrl.contains('vimeo.com')) {
      final videoId = videoUrl.split('/').last;
      return 'https://vumbnail.com/$videoId.jpg';
    }

    // Fallback to a fitness image
    return 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80';
  }

  String _formatElapsedTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  double _calculateProgress() {
    int totalSets = 0;
    int completedSets = 0;

    for (int i = 0; i < widget.workout.exercises.length; i++) {
      final exercise = widget.workout.exercises[i];
      totalSets += exercise.sets;

      if (i < _currentExerciseIndex) {
        // All sets for previous exercises are completed
        completedSets += exercise.sets;
      } else if (i == _currentExerciseIndex) {
        // Current exercise: count completed sets (current set - 1)
        completedSets += (_currentSet - 1);
      }
      // Future exercises contribute 0 completed sets
    }

    if (totalSets == 0) return 0.0;
    return completedSets / totalSets;
  }

  void _completeCurrentSet() {
    final reps = int.tryParse(_repsController.text) ?? 0;
    final weight = int.tryParse(_weightController.text) ?? 0;

    if (reps <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Please enter valid reps'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Store pending values and show feedback modal
    setState(() {
      _pendingReps = reps;
      _pendingWeight = weight;
      _showingFeedbackModal = true;
    });

    // Show feedback
    HapticFeedback.lightImpact();
  }

  void _onFeedbackSubmitted(String difficulty, bool personalizeWeight) {
    // Store feedback data (you could save this to database)
    debugPrint('Difficulty: $difficulty, Personalize: $personalizeWeight');
  }

  void _onFeedbackComplete() {
    if (_pendingReps == null || _pendingWeight == null) return;

    // Record the completed set
    setState(() {
      final currentExercise = widget.workout.exercises[_currentExerciseIndex];

      // Record the completed set
      _completedSets[_currentExerciseIndex].add({
        'set': _currentSet,
        'reps': _pendingReps!,
        'weight': _pendingWeight!,
        'timestamp': DateTime.now(),
      });

      // Hide feedback modal
      _showingFeedbackModal = false;
      _pendingReps = null;
      _pendingWeight = null;

      // Check if this is the last set of the last exercise
      final isLastSet = _currentSet >= currentExercise.sets;
      final isLastExercise =
          _currentExerciseIndex >= widget.workout.exercises.length - 1;

      if (isLastSet && isLastExercise) {
        // Workout complete - no rest needed
        _proceedToNextSetOrExercise();
      } else {
        // Show rest timer before next set/exercise
        _showingRestTimer = true;
      }
    });
  }

  void _onRestComplete() {
    setState(() {
      _showingRestTimer = false;
    });
    _proceedToNextSetOrExercise();
  }

  void _proceedToNextSetOrExercise() {
    setState(() {
      final currentExercise = widget.workout.exercises[_currentExerciseIndex];

      if (_currentSet < currentExercise.sets) {
        // Move to next set of current exercise
        _currentSet++;

        // Update inputs with next set's planned values
        if (currentExercise.reps.length > _currentSet - 1) {
          _repsController.text =
              currentExercise.reps[_currentSet - 1].toString();
        }
        if (currentExercise.weight != null &&
            currentExercise.weight!.length > _currentSet - 1) {
          _weightController.text =
              currentExercise.weight![_currentSet - 1].toString();
        }
      } else if (_currentExerciseIndex < widget.workout.exercises.length - 1) {
        // Move to next exercise
        _currentExerciseIndex++;
        _currentSet = 1;

        // Update inputs with new exercise's planned values
        final nextExercise = widget.workout.exercises[_currentExerciseIndex];
        if (nextExercise.reps.isNotEmpty) {
          _repsController.text = nextExercise.reps[0].toString();
        } else {
          _repsController.clear();
        }
        if (nextExercise.weight != null && nextExercise.weight!.isNotEmpty) {
          _weightController.text = nextExercise.weight![0].toString();
        } else {
          _weightController.clear();
        }
      } else {
        // Workout complete - navigate to completion screen
        _navigateToCompletion();
      }
    });
  }

  void _navigateToCompletion() {
    // Calculate total workout time
    final totalMinutes = (_elapsedSeconds / 60).round();

    // Calculate total sets completed and flatten completed sets
    List<Map<String, dynamic>> allCompletedSets = [];
    for (var exerciseSets in _completedSets) {
      allCompletedSets.addAll(exerciseSets);
    }

    // Navigate to completion screen
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => WorkoutCompletionScreen(
          workout: widget.workout,
          totalMinutes: totalMinutes,
          totalSets: allCompletedSets.length,
          completedSets: allCompletedSets,
        ),
      ),
    );
  }

  String? _getNextExerciseInfo() {
    final currentExercise = widget.workout.exercises[_currentExerciseIndex];

    if (_currentSet < currentExercise.sets) {
      // Next set of same exercise
      return null; // Will show "Set X of Y" in rest timer
    } else if (_currentExerciseIndex < widget.workout.exercises.length - 1) {
      // Next exercise
      return widget.workout.exercises[_currentExerciseIndex + 1].exercise.name;
    }
    return null; // Last exercise, last set
  }

  int _getRestDuration() {
    final currentExercise = widget.workout.exercises[_currentExerciseIndex];
    return currentExercise.restInterval ?? 90; // Default 90 seconds
  }

  void _completeWorkout() async {
    try {
      await _workoutService.completeWorkout(widget.workout.id);
      _navigateToCompletion();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing workout: $e'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColorsTheme.surface(context),
        title: Text(
          'Exit Workout?',
          style: AppTypography.heading3.copyWith(
            color: AppColorsTheme.textPrimary(context),
          ),
        ),
        content: Text(
          'Your progress will be saved, but the workout will remain active.',
          style: AppTypography.body2.copyWith(
            color: AppColorsTheme.textSecondary(context),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: AppTypography.buttonMedium.copyWith(
                color: AppColorsTheme.textSecondary(context),
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Exit workout
            },
            child: Text(
              'Exit',
              style: AppTypography.buttonMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
