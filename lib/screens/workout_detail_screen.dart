import 'package:flutter/material.dart';
import '../models/workout.dart';
import '../services/workout_service.dart';
import '../design_system/design_system.dart';
import '../widgets/offline_status_indicator.dart';

import 'workout_session_screen.dart';

class WorkoutDetailScreen extends StatefulWidget {
  final Workout workout;

  const WorkoutDetailScreen({
    super.key,
    required this.workout,
  });

  @override
  State<WorkoutDetailScreen> createState() => _WorkoutDetailScreenState();
}

class _WorkoutDetailScreenState extends State<WorkoutDetailScreen> {
  final WorkoutService _workoutService = WorkoutService();

  @override
  Widget build(BuildContext context) {
    return WorkoutScaffold(
      onClose: () => Navigator.pop(context),
      showBottomNavigation: false,
      scrollable: true,
      bodyPadding: EdgeInsets.zero,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hero section with background image and stats overlay
          _buildHeroSection(),
          // Exercise List
          Padding(
            padding: const EdgeInsets.all(AppSpacing.screenPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildExerciseList(),
                const SizedBox(height: AppSpacing.sectionSpacing),
                _buildStartButton(),
                const SizedBox(height: AppSpacing.xxxl),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeroSection() {
    final topPadding = MediaQuery.of(context).padding.top;

    return SizedBox(
      height: 320 + topPadding,
      width: double.infinity,
      child: Stack(
        children: [
          // Background image
          Container(
            height: 280 + topPadding,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  AppColors.primary.withValues(alpha: 0.8),
                  AppColors.primaryDark.withValues(alpha: 0.9),
                ],
              ),
              image: DecorationImage(
                image: NetworkImage(
                  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                ),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  Colors.black.withValues(alpha: 0.4),
                  BlendMode.darken,
                ),
              ),
            ),
          ),
          // Offline Status Indicator
          Positioned(
            top: topPadding + 16,
            left: 16,
            right: 16,
            child: const OfflineStatusIndicator(),
          ),
          // Workout title and description
          Positioned(
            top: topPadding + 60,
            left: 24,
            right: 24,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.workout.name,
                  style: AppTypography.display1.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                Text(
                  widget.workout.workoutSummary,
                  style: AppTypography.body1.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          // Stats cards overlay
          Positioned(
            bottom: 0,
            left: 24,
            right: 24,
            child: _buildStatsOverlay(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsOverlay() {
    final estimatedDuration = _calculateEstimatedDuration();
    final estimatedCalories = _calculateEstimatedCalories();

    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: AppColorsTheme.cardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.schedule,
              iconColor: AppColors.primary,
              value: estimatedDuration,
              label: 'Duration',
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColorsTheme.borderLight(context),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.local_fire_department,
              iconColor: AppColors.error,
              value: '$estimatedCalories kcal',
              label: 'Calories',
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: AppColorsTheme.borderLight(context),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.fitness_center,
              iconColor: AppColors.success,
              value: '${widget.workout.exerciseCount}',
              label: 'Exercises',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required Color iconColor,
    required String value,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: iconColor,
              ),
              const SizedBox(width: 4),
              Text(
                value,
                style: AppTypography.heading3.copyWith(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColorsTheme.textPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: AppTypography.caption.copyWith(
              color: AppColorsTheme.textSecondary(context),
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildExerciseList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Exercise List',
          style: AppTypography.heading2.copyWith(
            color: AppColorsTheme.textPrimary(context),
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        ...widget.workout.exercises.map((exercise) {

          return Container(
            margin: const EdgeInsets.only(bottom: AppSpacing.md),
            decoration: BoxDecoration(
              color: AppColorsTheme.cardBackground(context),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColorsTheme.borderLight(context),
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Exercise thumbnail/icon
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: exercise.exercise.videoUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.network(
                              _getExerciseThumbnail(exercise.exercise.videoUrl!),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Icon(
                                Icons.play_circle_fill,
                                color: AppColors.primary,
                                size: 28,
                              ),
                            ),
                          )
                        : const Icon(
                            Icons.fitness_center,
                            color: AppColors.primary,
                            size: 28,
                          ),
                  ),
                  const SizedBox(width: AppSpacing.lg),
                  // Exercise details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          exercise.exercise.name,
                          style: AppTypography.heading3.copyWith(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppColorsTheme.textPrimary(context),
                          ),
                        ),
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          exercise.formattedReps,
                          style: AppTypography.body1.copyWith(
                            color: AppColorsTheme.textSecondary(context),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // More options button
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColorsTheme.surface(context),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.more_vert,
                      color: AppColorsTheme.textSecondary(context),
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildStartButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primaryDark],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _startWorkoutSession,
          borderRadius: BorderRadius.circular(16),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.workout.isActive ? 'Continue Workout' : 'Start Workout',
                  style: AppTypography.heading3.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _calculateEstimatedDuration() {
    // Calculate based on sets, reps, and rest intervals
    int totalSeconds = 0;

    for (final exercise in widget.workout.exercises) {
      // Estimate 3 seconds per rep
      final repsPerSet = exercise.reps.isNotEmpty ? exercise.reps.first : 10;
      final workingTime = exercise.sets * repsPerSet * 3;

      // Add rest time between sets
      final restTime = (exercise.sets - 1) * (exercise.restInterval ?? 60);

      totalSeconds += workingTime + restTime;
    }

    // Add transition time between exercises (30 seconds each)
    totalSeconds += (widget.workout.exercises.length - 1) * 30;

    final minutes = totalSeconds ~/ 60;
    return '$minutes min';
  }

  int _calculateEstimatedCalories() {
    // Rough estimation: 5-8 calories per minute depending on intensity
    final durationStr = _calculateEstimatedDuration();
    final minutes = int.tryParse(durationStr.replaceAll(' min', '')) ?? 30;

    // Use 6 calories per minute as average
    return minutes * 6;
  }

  String _getExerciseThumbnail(String videoUrl) {
    // For Vimeo URLs, generate thumbnail
    if (videoUrl.contains('vimeo.com')) {
      final videoId = videoUrl.split('/').last;
      return 'https://vumbnail.com/$videoId.jpg';
    }

    // Fallback to a fitness image
    return 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80';
  }

  void _startWorkoutSession() async {
    try {
      // Start the workout session in the database
      await _workoutService.startWorkout(widget.workout.id);

      if (mounted) {
        // Navigate to workout session screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WorkoutSessionScreen(workout: widget.workout),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting workout: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
